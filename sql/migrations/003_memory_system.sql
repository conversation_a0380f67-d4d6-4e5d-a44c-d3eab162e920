-- Migration: Enhanced Memory System with pgvector
-- Purpose: Create comprehensive memory storage for agent knowledge base
-- Date: 2025-01-10
-- Dependencies: Requires pgvector extension

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create enhanced memory embeddings table
CREATE TABLE IF NOT EXISTS memory_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trace_id TEXT,
    span_id TEXT,
    agent_name TEXT NOT NULL,
    mem_type TEXT NOT NULL DEFAULT 'agent_action',
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    json_blob JSONB,
    metadata JSONB DEFAULT '{}',
    importance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ, -- Optional expiration for temporary memories
    tags TEXT[] DEFAULT '{}' -- For categorization and filtering
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_agent_name ON memory_embeddings(agent_name);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_mem_type ON memory_embeddings(mem_type);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_created_at ON memory_embeddings(created_at);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_importance ON memory_embeddings(importance_score);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_tags ON memory_embeddings USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_metadata ON memory_embeddings USING GIN(metadata);

-- Create vector similarity index (HNSW for better performance)
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_vector 
ON memory_embeddings USING hnsw (embedding vector_cosine_ops);

-- Create function for similarity search with advanced filtering
CREATE OR REPLACE FUNCTION match_memories(
    query_embedding vector(1536),
    agent_filter TEXT DEFAULT NULL,
    mem_type_filter TEXT DEFAULT NULL,
    match_threshold FLOAT DEFAULT 0.78,
    match_count INT DEFAULT 5,
    min_importance FLOAT DEFAULT 0.0,
    tag_filters TEXT[] DEFAULT NULL,
    time_window_hours INT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    agent_name TEXT,
    mem_type TEXT,
    content TEXT,
    json_blob JSONB,
    metadata JSONB,
    importance_score DECIMAL,
    similarity FLOAT,
    created_at TIMESTAMPTZ,
    tags TEXT[]
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.agent_name,
        m.mem_type,
        m.content,
        m.json_blob,
        m.metadata,
        m.importance_score,
        (1 - (m.embedding <=> query_embedding)) as similarity,
        m.created_at,
        m.tags
    FROM memory_embeddings m
    WHERE 
        (1 - (m.embedding <=> query_embedding)) > match_threshold
        AND (agent_filter IS NULL OR m.agent_name = agent_filter)
        AND (mem_type_filter IS NULL OR m.mem_type = mem_type_filter)
        AND m.importance_score >= min_importance
        AND (tag_filters IS NULL OR m.tags && tag_filters)
        AND (time_window_hours IS NULL OR m.created_at > NOW() - INTERVAL '1 hour' * time_window_hours)
        AND (m.expires_at IS NULL OR m.expires_at > NOW())
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;

-- Create function for memory consolidation (merge similar memories)
CREATE OR REPLACE FUNCTION consolidate_memories(
    similarity_threshold FLOAT DEFAULT 0.95,
    agent_filter TEXT DEFAULT NULL
)
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
    consolidated_count INT := 0;
    memory_record RECORD;
    similar_record RECORD;
BEGIN
    -- Find and merge highly similar memories
    FOR memory_record IN 
        SELECT * FROM memory_embeddings 
        WHERE (agent_filter IS NULL OR agent_name = agent_filter)
        ORDER BY created_at DESC
    LOOP
        -- Find similar memories
        FOR similar_record IN
            SELECT * FROM memory_embeddings m
            WHERE m.id != memory_record.id
            AND (agent_filter IS NULL OR m.agent_name = agent_filter)
            AND (1 - (m.embedding <=> memory_record.embedding)) > similarity_threshold
            AND m.created_at < memory_record.created_at
        LOOP
            -- Update the newer memory with consolidated information
            UPDATE memory_embeddings 
            SET 
                content = memory_record.content || ' | ' || similar_record.content,
                metadata = memory_record.metadata || similar_record.metadata,
                importance_score = GREATEST(memory_record.importance_score, similar_record.importance_score),
                tags = array(SELECT DISTINCT unnest(memory_record.tags || similar_record.tags)),
                updated_at = NOW()
            WHERE id = memory_record.id;
            
            -- Delete the older similar memory
            DELETE FROM memory_embeddings WHERE id = similar_record.id;
            consolidated_count := consolidated_count + 1;
        END LOOP;
    END LOOP;
    
    RETURN consolidated_count;
END;
$$;

-- Create memory statistics view
CREATE OR REPLACE VIEW memory_stats AS
SELECT 
    agent_name,
    mem_type,
    COUNT(*) as memory_count,
    AVG(importance_score) as avg_importance,
    MAX(created_at) as last_memory_at,
    COUNT(DISTINCT tags) as unique_tags,
    AVG(length(content)) as avg_content_length
FROM memory_embeddings
WHERE expires_at IS NULL OR expires_at > NOW()
GROUP BY agent_name, mem_type
ORDER BY memory_count DESC;

-- Create function to cleanup expired memories
CREATE OR REPLACE FUNCTION cleanup_expired_memories()
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INT;
BEGIN
    DELETE FROM memory_embeddings 
    WHERE expires_at IS NOT NULL AND expires_at <= NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- Create memory usage analytics table
CREATE TABLE IF NOT EXISTS memory_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_name TEXT NOT NULL,
    query_type TEXT NOT NULL,
    query_embedding vector(1536),
    results_count INT DEFAULT 0,
    avg_similarity FLOAT DEFAULT 0.0,
    execution_time_ms INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for analytics
CREATE INDEX IF NOT EXISTS idx_memory_analytics_agent_name ON memory_analytics(agent_name);
CREATE INDEX IF NOT EXISTS idx_memory_analytics_created_at ON memory_analytics(created_at);

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON memory_embeddings TO your_app_user;
-- GRANT SELECT ON memory_stats TO your_app_user;
-- GRANT SELECT, INSERT ON memory_analytics TO your_app_user;

-- Example usage queries:
-- 
-- -- Search for memories
-- SELECT * FROM match_memories(
--     query_embedding := '[0.1, 0.2, ...]'::vector,
--     agent_filter := 'trading_agent',
--     mem_type_filter := 'market_analysis',
--     match_threshold := 0.8,
--     match_count := 10
-- );
-- 
-- -- View memory statistics
-- SELECT * FROM memory_stats WHERE agent_name = 'trading_agent';
-- 
-- -- Cleanup expired memories
-- SELECT cleanup_expired_memories();
-- 
-- -- Consolidate similar memories
-- SELECT consolidate_memories(0.95, 'trading_agent');
